// app/pizza-maker/page.tsx
'use client'

import React, { useEffect, useState } from 'react';
import { AlertCircle, Clock } from 'lucide-react';
import { ITEM_STATUSES, ORDER_STATUSES, OrderDocument } from '@/model/order';
import { Heading } from "@/app/components/layout/Heading";
import { useTranslations } from 'next-intl';
import { timeslotToLocalTime } from "@/lib/time";
import { useItems } from "@/lib/fetch/item";
import { Loading } from "@/app/components/Loading";
import { updateOrder, useOrders } from "@/lib/fetch/order";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import ItemCustomizationDisplay from '@/app/components/order/ItemCustomizationDisplay';

const PizzaMakerStation = () => {
    const queryClient = useQueryClient();
    const [upcomingPizzas, setUpcomingPizzas] = useState<Map<string, number>>(new Map());
    const [recentlyMade, setRecentlyMade] = useState<string[]>([]);
    const [error, setError] = useState('');

    const t = useTranslations();

    const { data: items, error: itemsError, isFetching: isFetchingItems } = useItems()
    const { data: orders, error: ordersError } = useOrders(5000)

    const updateOrderMutation = useMutation({
        mutationFn: async ({ orderId, order }: {
            orderId: string;
            order: OrderDocument
        }) => updateOrder(orderId, order),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ['orders'] }),
    });

    // Calculate what pizzas need to be made
    const calculateUpcomingPizzas = (orders: OrderDocument[]) => {
        const pizzaCount = new Map<string, number>();
        for (const order of orders) {
            if (order.status !== ORDER_STATUSES.ORDERED && order.status !== ORDER_STATUSES.ACTIVE) {
                continue;
            }

            for (const item of order.items) {
                if ((item.status === 'prepping' || item.status === 'readyToCook') && item.item.type === 'pizza') {
                    const key = item.item.name;
                    pizzaCount.set(key, (pizzaCount.get(key) ?? 0) + 1);
                }
            }
        }

        setUpcomingPizzas(pizzaCount);
    };

    useEffect(() => {
        if (orders) {
            const filteredOrders = orders.filter((order) =>
                order.status === ORDER_STATUSES.ORDERED || order.status === ORDER_STATUSES.ACTIVE
            )
            calculateUpcomingPizzas(filteredOrders);
        }
    }, [items, orders]);
    useEffect(() => {
        if (itemsError) {
            setError(itemsError.message)
        } else if (ordersError) {
            setError(ordersError.message)
        }
    }, [ordersError, itemsError]);

    if (isFetchingItems) {
        return <Loading message={t('loading_menu')}/>
    }

    if (!items || !orders) {
        return null;
    }



    // Mark items as ready (advance to next stage)
    const markPizzaReady = async (itemId: string) => {
        // Find the first order that needs this items type
        const targetOrder = orders.find(order =>
            (order.status === ORDER_STATUSES.ORDERED || order.status === ORDER_STATUSES.ACTIVE) &&
            order.items.some(item =>
                item.item._id.toString() === itemId &&
                (item.status === 'prepping' || item.status === 'readyToCook')
            )
        );

        if (!targetOrder) {
            setError(`No pending orders for that item found`);
            return;
        }

        // Find the specific item index
        const itemIndex = targetOrder.items.findIndex(item =>
            item.item._id.toString() === itemId &&
            (item.status === 'prepping' || item.status === 'readyToCook')
        );

        // Update item status based on current status
        const updatedOrder = { ...targetOrder } as OrderDocument;
        const currentStatus = updatedOrder.items[itemIndex].status;

        if (currentStatus === 'prepping') {
            updatedOrder.items[itemIndex].status = ITEM_STATUSES.READY_TO_COOK;
        } else if (currentStatus === 'readyToCook') {
            updatedOrder.items[itemIndex].status = ITEM_STATUSES.COOKING;
        }

        updateOrderMutation.mutate({ orderId: targetOrder._id.toString(), order: updatedOrder })
    };

    return (
        <div>
            <Heading title={t('admin.prepare.title')} description={t('admin.prepare.subtitle')}
                     icon={<Clock className="w-10 h-10 text-gray-900"/>}/>


            <div className="flex flex-col md:flex-row gap-5">
                <div className="bg-white p-4 md:p-8 rounded-2xl w-full md:w-1/3">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800">Active Orders</h3>
                    <div className="flex flex-col gap-3 max-h-[20rem] md:max-h-fit overflow-y-scroll">
                        {orders.map(order => {
                            const activeItems = order.items.filter(item =>
                                item.status === ITEM_STATUSES.PREPPING || item.status === ITEM_STATUSES.READY_TO_COOK
                            );

                            if (activeItems.length === 0) return null;

                            return (
                                <div key={order._id.toString()} className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                                    {/* Order Header */}
                                    <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-200">
                                        <div className="flex items-center gap-2">
                                            <span className="font-semibold text-gray-800">{order.name}</span>
                                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                                {order.status}
                                            </span>
                                        </div>
                                        <span className="text-sm bg-gray-200 px-2 py-1 rounded-full">
                                            {timeslotToLocalTime(order.timeslot)}
                                        </span>
                                    </div>

                                    {/* Order Items */}
                                    <div className="space-y-2">
                                        {activeItems.map((item, index) => (
                                            <div
                                                key={`${order._id.toString()}-${item.item._id.toString()}-${item.status}-${index}`}
                                                className={`border rounded-md p-2 text-sm transition-colors ${
                                                    item.status === ITEM_STATUSES.READY_TO_COOK
                                                        ? 'bg-orange-50 border-orange-200'
                                                        : 'bg-white border-gray-200'
                                                }`}>
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-2 mb-1">
                                                            <span className="font-medium text-gray-900">{item.item.name}</span>
                                                            <span className={`text-xs px-2 py-0.5 rounded-full ${
                                                                item.status === ITEM_STATUSES.READY_TO_COOK
                                                                    ? 'bg-orange-100 text-orange-800'
                                                                    : 'bg-gray-100 text-gray-700'
                                                            }`}>
                                                                {item.status === ITEM_STATUSES.READY_TO_COOK ? 'Ready to Cook' : 'Prepping'}
                                                            </span>
                                                        </div>
                                                        <ItemCustomizationDisplay item={item.item} showTitle={false} compact={true} />

                                                        {/* Additional item details */}
                                                        {item.item.dietary && (
                                                            <div className="flex flex-wrap gap-1 mt-1">
                                                                {typeof item.item.dietary === 'object' ? (
                                                                    <>
                                                                        {item.item.dietary.vegetarian && <span className="text-xs bg-green-100 text-green-700 px-1 py-0.5 rounded">Vegetarian</span>}
                                                                        {item.item.dietary.vegan && <span className="text-xs bg-green-100 text-green-700 px-1 py-0.5 rounded">Vegan</span>}
                                                                        {item.item.dietary.gluten_free && <span className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded">Gluten Free</span>}
                                                                        {item.item.dietary.dairy_free && <span className="text-xs bg-purple-100 text-purple-700 px-1 py-0.5 rounded">Dairy Free</span>}
                                                                    </>
                                                                ) : (
                                                                    <span className="text-xs bg-gray-100 text-gray-700 px-1 py-0.5 rounded">{item.item.dietary}</span>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>

                                    {/* Order Comment */}
                                    {order.comment && (
                                        <div className="mt-2 pt-2 border-t border-gray-200">
                                            <span className="text-xs text-gray-600 font-medium">Note: </span>
                                            <span className="text-xs text-gray-700">{order.comment}</span>
                                        </div>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                    {upcomingPizzas.size === 0 && (
                        <div className="text-center py-8">
                            <span className="text-gray-400 text-lg">{t('admin.prepare.no_open_orders')} 🎉</span>
                        </div>
                    )}
                </div>

                {/* Pizza buttons grid */}
                <div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                        {items.map(item => {
                            const pendingCount = upcomingPizzas.get(item.name) ?? 0;
                            const isRecent = recentlyMade.includes(item.name);

                            // Get status breakdown for this pizza type
                            const statusBreakdown = orders.reduce((acc, order) => {
                                order.items.forEach(orderItem => {
                                    if (orderItem.item.name === item.name && orderItem.item.type === 'pizza') {
                                        if (orderItem.status === 'prepping') acc.prepping++;
                                        else if (orderItem.status === 'readyToCook') acc.readyToCook++;
                                    }
                                });
                                return acc;
                            }, { prepping: 0, readyToCook: 0 });

                            return (
                                <button
                                    key={`${item.id}-${item.name}`}
                                    className={`
                aspect-4/3 bg-opacity-90 rounded-3xl p-3 md:p-4
                shadow-2xl transform transition-all duration-200
                ${pendingCount > 0 ? 'hover:scale-105 active:scale-95 cursor-pointer bg-gradient-to-br from-blue-400 to-blue-600' : 'opacity-50 cursor-not-allowed bg-gray-400'}
                ${isRecent ? 'ring-4 ring-green-400 ring-opacity-75' : ''}
              `}
                                    onClick={() => pendingCount > 0 && markPizzaReady(item._id.toString())}
                                    disabled={pendingCount === 0}
                                >
                                    <div className="flex flex-col items-center justify-center h-full">
                                        <h3 className="text-2xl md:text-3xl font-bold text-white">{item.name}</h3>
                                        {item.dietary && (
                                            <div className="text-sm text-white opacity-90 mt-1 flex flex-wrap gap-1 justify-center">
                                                {typeof item.dietary === 'object' ? (
                                                    <>
                                                        {item.dietary.vegetarian && <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">Vegetarian</span>}
                                                        {item.dietary.vegan && <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">Vegan</span>}
                                                        {item.dietary.gluten_free && <span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs">Gluten Free</span>}
                                                        {item.dietary.dairy_free && <span className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full text-xs">Dairy Free</span>}
                                                    </>
                                                ) : (
                                                    <span>{item.dietary}</span>
                                                )}
                                            </div>
                                        )}
                                        {pendingCount > 0 && (
                                            <div className="mt-4 space-y-2">
                                                <div className="bg-white bg-opacity-25 rounded-2xl px-4 py-2">
                                                    <span className="text-lg font-medium text-white">{pendingCount} total</span>
                                                </div>
                                                {statusBreakdown.prepping > 0 && (
                                                    <div className="bg-yellow-100 bg-opacity-90 rounded-xl px-3 py-1">
                                                        <span className="text-sm font-medium text-yellow-800">{statusBreakdown.prepping} prepping</span>
                                                    </div>
                                                )}
                                                {statusBreakdown.readyToCook > 0 && (
                                                    <div className="bg-orange-100 bg-opacity-90 rounded-xl px-3 py-1">
                                                        <span className="text-sm font-medium text-orange-800">{statusBreakdown.readyToCook} ready to cook</span>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                        {isRecent && (
                                            <span className="text-green-300 text-lg mt-2">✓ Marked ready!</span>
                                        )}
                                    </div>
                                </button>
                            );
                        })}
                    </div>
                </div>
            </div>

            {error && (
                <div
                    className="fixed bottom-8 right-8 bg-red-500 text-black rounded-lg px-6 py-4 flex items-center gap-3">
                    <AlertCircle className="w-6 h-6"/>
                    <span>{error}</span>
                </div>
            )}
        </div>
    );
};

export default PizzaMakerStation;
