'use client'

import React from 'react';
import { ItemDocument } from '@/model/item';
import { Plus, Minus, ChefHat } from 'lucide-react';

interface ItemCustomizationDisplayProps {
    item: ItemDocument;
    showTitle?: boolean;
    compact?: boolean;
}

interface CustomizationDetails {
    size?: string;
    added_toppings?: Record<string, number>;
    removed_ingredients?: string[];
}

const ItemCustomizationDisplay: React.FC<ItemCustomizationDisplayProps> = ({ 
    item, 
    showTitle = true, 
    compact = false 
}) => {
    // Extract customization details from the item
    const customizationDetails = (item as any).customization_details as CustomizationDetails;
    
    if (!customizationDetails) {
        return null;
    }

    const { size, added_toppings, removed_ingredients } = customizationDetails;
    
    // Check if there are any customizations to display
    const hasCustomizations = size !== 'regular' || 
                             (added_toppings && Object.keys(added_toppings).length > 0) || 
                             (removed_ingredients && removed_ingredients.length > 0);

    if (!hasCustomizations) {
        return null;
    }

    if (compact) {
        return (
            <div className="text-xs text-gray-500 mt-1">
                {size && size !== 'regular' && (
                    <span className="inline-flex items-center gap-1 mr-2">
                        <ChefHat className="w-3 h-3" />
                        {size}
                    </span>
                )}
                {added_toppings && Object.keys(added_toppings).length > 0 && (
                    <span className="inline-flex items-center gap-1 mr-2">
                        <Plus className="w-3 h-3 text-green-600" />
                        {Object.keys(added_toppings).length} extra{Object.keys(added_toppings).length > 1 ? 's' : ''}
                    </span>
                )}
                {removed_ingredients && removed_ingredients.length > 0 && (
                    <span className="inline-flex items-center gap-1">
                        <Minus className="w-3 h-3 text-red-600" />
                        {removed_ingredients.length} removed
                    </span>
                )}
            </div>
        );
    }

    return (
        <div className="mt-3 p-3 bg-gray-50 rounded-lg border border-gray-100">
            {showTitle && (
                <div className="flex items-center gap-2 mb-2">
                    <ChefHat className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">Customizations</span>
                </div>
            )}
            
            <div className="space-y-2">
                {/* Size customization */}
                {size && size !== 'regular' && (
                    <div className="flex items-center gap-2">
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                            Size: {size}
                        </span>
                    </div>
                )}

                {/* Added toppings */}
                {added_toppings && Object.keys(added_toppings).length > 0 && (
                    <div>
                        <div className="flex items-center gap-1 mb-1">
                            <Plus className="w-3 h-3 text-green-600" />
                            <span className="text-xs font-medium text-green-700">Added:</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                            {Object.entries(added_toppings).map(([topping, quantity]) => (
                                <span 
                                    key={topping}
                                    className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full"
                                >
                                    {topping} {quantity > 1 ? `(×${quantity})` : ''}
                                </span>
                            ))}
                        </div>
                    </div>
                )}

                {/* Removed ingredients */}
                {removed_ingredients && removed_ingredients.length > 0 && (
                    <div>
                        <div className="flex items-center gap-1 mb-1">
                            <Minus className="w-3 h-3 text-red-600" />
                            <span className="text-xs font-medium text-red-700">Removed:</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                            {removed_ingredients.map((ingredient) => (
                                <span 
                                    key={ingredient}
                                    className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full"
                                >
                                    {ingredient}
                                </span>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ItemCustomizationDisplay;
