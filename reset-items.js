// Using built-in fetch (Node.js 18+)

async function resetItems() {
    try {
        console.log('Starting item reset process...');
        
        // Step 1: Login to get auth token
        console.log('1. Authenticating...');
        const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ token: 'admin123' }),
        });

        if (!loginResponse.ok) {
            throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`);
        }

        // Extract cookies from login response
        const cookies = loginResponse.headers.get('set-cookie');
        console.log('✓ Authentication successful');

        // Step 2: Call reset-items endpoint
        console.log('2. Resetting items...');
        const resetResponse = await fetch('http://localhost:3000/api/reset-items', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cookie': cookies || '',
            },
        });

        if (!resetResponse.ok) {
            const errorText = await resetResponse.text();
            throw new Error(`Reset failed: ${resetResponse.status} ${resetResponse.statusText}\n${errorText}`);
        }

        const result = await resetResponse.json();
        console.log('✓ Items reset successfully!');
        console.log(`   - Deleted items: ${result.deletedItems}`);
        console.log(`   - Inserted items: ${result.insertedItems}`);
        console.log(`   - Timestamp: ${result.timestamp}`);

    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

resetItems();
